
import React, { useRef, useEffect } from 'react';

// Define an interface for our special text objects
interface SpecialText {
    text: string;
    x: number; // column index
    y: number; // y-position in pixels
    color: string;
    fontWeight: string;
}

const AsciiBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    // Use a ref to store mutable animation state that doesn't trigger re-renders
    const animationState = useRef({
        animationFrameId: 0,
        intervalId: 0,
        drops: [] as number[],
        specialTexts: [] as SpecialText[],
        columns: 0,
    });

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // Use a ref for state to avoid re-running the effect on change
        const state = animationState.current;

        const characters = 'アァカサタナハマヤャラワガザダバパイィキシチニヒミリヰギジヂビピウゥクスツヌフムユュルグズブプエェケセテネヘメレヱゲゼデベペオォコソトノホモヨョロヲゴゾドボポヴッン0123456789';
        const fontSize = 14;
        
        const init = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            state.columns = Math.floor(canvas.width / fontSize);
            state.drops = [];
            for (let i = 0; i < state.columns; i++) {
                // Randomize starting drop position for a more organic feel
                state.drops[i] = Math.floor(Math.random() * (canvas.height / fontSize));
            }
            // Do not clear special texts on resize, just let them fall off screen
        };

        init();
        window.addEventListener('resize', init);
        
        // Add a new special text every second
        if (state.intervalId) clearInterval(state.intervalId); // Clear previous interval if effect re-runs
        state.intervalId = window.setInterval(() => {
            if (state.columns > 0) {
                 const text = "xDepredadorxD";
                 const textLengthInColumns = text.length;
                 if (state.columns > textLengthInColumns) {
                    const maxColumn = state.columns - textLengthInColumns;
                    state.specialTexts.push({
                        text,
                        x: Math.floor(Math.random() * maxColumn),
                        y: fontSize, // Start at the top
                        color: '#FFFFFF', // Bright white
                        fontWeight: 'bold',
                    });
                 }
            }
        }, 1000);

        const draw = () => {
            // Semi-transparent background for the trail effect
            ctx.fillStyle = 'rgba(17, 24, 39, 0.05)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw the regular green rain
            ctx.fillStyle = '#4ade80'; // Tailwind green-400
            ctx.font = `normal ${fontSize}px monospace`;

            for (let i = 0; i < state.drops.length; i++) {
                const char = characters.charAt(Math.floor(Math.random() * characters.length));
                ctx.fillText(char, i * fontSize, state.drops[i] * fontSize);

                // Reset drop to top when it goes off screen
                if (state.drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                    state.drops[i] = 0;
                }
                state.drops[i]++;
            }

            // Draw and update special texts
            // Iterate backwards to safely remove elements
            for (let i = state.specialTexts.length - 1; i >= 0; i--) {
                const sText = state.specialTexts[i];
                ctx.fillStyle = sText.color;
                ctx.font = `${sText.fontWeight} ${fontSize}px monospace`;
                ctx.fillText(sText.text, sText.x * fontSize, sText.y);

                // Move the text down
                sText.y += fontSize;

                // If text is off-screen, remove it
                if (sText.y - fontSize > canvas.height) {
                    state.specialTexts.splice(i, 1);
                }
            }
        };

        const animate = () => {
            draw();
            state.animationFrameId = window.requestAnimationFrame(animate);
        };
        
        // Start animation
        if (state.animationFrameId) cancelAnimationFrame(state.animationFrameId); // Clear previous frame
        animate();

        // Cleanup function
        return () => {
            window.removeEventListener('resize', init);
            window.cancelAnimationFrame(state.animationFrameId);
            window.clearInterval(state.intervalId);
        };
    }, []); // Empty dependency array ensures this runs only once on mount and cleanup on unmount

    return <canvas ref={canvasRef} className="absolute top-0 left-0 w-full h-full" />;
};

export default AsciiBackground;
