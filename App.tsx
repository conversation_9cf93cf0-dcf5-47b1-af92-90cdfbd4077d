
import React, { useState, useCallback, useEffect } from 'react';
import { KeyIcon, LockIcon, ChipIcon, TrashIcon, ClockIcon, ClipboardIcon, CheckIcon, QrCodeIcon } from './components/icons';
import DisplayField from './components/DisplayField';
import AsciiBackground from './components/AsciiBackground';
import QRCode from 'qrcode';

// Helper function to convert ArrayBuffer to Hex string
const bufferToHex = (buffer: ArrayBuffer): string => {
    return [...new Uint8Array(buffer)]
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');
};

interface HistoryEntry {
    id: number;
    machineId: string;
    activationKey: string;
    timestamp: string;
}

const App: React.FC = () => {
    const [combinedInput, setCombinedInput] = useState<string>('');
    const [secretKey, setSecretKey] = useState<string>('');
    const [activationKey, setActivationKey] = useState<string>('');
    const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');
    const [isGenerating, setIsGenerating] = useState<boolean>(false);
    const [error, setError] = useState<string>('');
    const [history, setHistory] = useState<HistoryEntry[]>([]);
    const [isSecretKeySaved, setIsSecretKeySaved] = useState<boolean>(false);
    const [copiedKey, setCopiedKey] = useState<string | null>(null);

    useEffect(() => {
        try {
            const savedSecretKey = localStorage.getItem('secretKey');
            if (savedSecretKey) {
                setSecretKey(savedSecretKey);
                setIsSecretKeySaved(true);
            }

            const savedHistory = localStorage.getItem('activationHistory');
            if (savedHistory) {
                setHistory(JSON.parse(savedHistory));
            }
        } catch (e) {
            console.error("Failed to read from localStorage", e);
        }
    }, []);
    
    const handleSaveSecretKey = useCallback(() => {
        if (secretKey) {
            localStorage.setItem('secretKey', secretKey);
            setIsSecretKeySaved(true);
        }
    }, [secretKey]);

    const handleDeleteSecretKey = useCallback(() => {
        localStorage.removeItem('secretKey');
        setSecretKey('');
        setIsSecretKeySaved(false);
    }, []);

    const handleClearHistory = useCallback(() => {
        localStorage.removeItem('activationHistory');
        setHistory([]);
    }, []);

    const handleCopyHistoryKey = useCallback((key: string) => {
        navigator.clipboard.writeText(key);
        setCopiedKey(key);
        setTimeout(() => setCopiedKey(null), 2000);
    }, []);

    const handleGenerateKey = useCallback(async () => {
        if (!combinedInput || !secretKey) {
            setError('Por favor, ingrese el ID de la máquina/MAC y la clave secreta.');
            return;
        }
        setError('');
        setIsGenerating(true);
        setActivationKey('');
        setQrCodeDataUrl('');

        try {
            const encoder = new TextEncoder();
            const keyData = encoder.encode(secretKey);
            const messageData = encoder.encode(combinedInput);

            const importedKey = await crypto.subtle.importKey(
                'raw',
                keyData,
                { name: 'HMAC', hash: 'SHA-256' },
                false,
                ['sign']
            );

            const signature = await crypto.subtle.sign(
                'HMAC',
                importedKey,
                messageData
            );

            const hash = bufferToHex(signature);
            const firstPart = hash.substring(0, 5);
            const lastPart = hash.substring(hash.length - 7);
            const newActivationKey = `${firstPart}${lastPart}`.toUpperCase();
            setActivationKey(newActivationKey);

            // Generate QR Code
            try {
                const qrUrl = await QRCode.toDataURL(newActivationKey, {
                    width: 200,
                    margin: 1,
                    errorCorrectionLevel: 'H',
                    color: {
                        dark: '#4ade80', // green-400
                        light: '#00000000' // transparent background
                    }
                });
                setQrCodeDataUrl(qrUrl);
            } catch (qrErr) {
                console.error("Failed to generate QR Code", qrErr);
            }

            setHistory(prevHistory => {
                const newEntry: HistoryEntry = {
                    id: Date.now(),
                    machineId: combinedInput,
                    activationKey: newActivationKey,
                    timestamp: new Date().toISOString(),
                };
                const updatedHistory = [newEntry, ...prevHistory].slice(0, 50); // Keep last 50 entries
                localStorage.setItem('activationHistory', JSON.stringify(updatedHistory));
                return updatedHistory;
            });

        } catch (err) {
            console.error('Error al generar la clave:', err);
            setError('Ocurrió un error al generar la clave. Verifique la consola.');
        } finally {
            setIsGenerating(false);
        }
    }, [combinedInput, secretKey]);

    return (
        <div className="min-h-screen text-white flex items-center justify-center p-4 font-sans relative overflow-hidden">
            <AsciiBackground />
            <div className="w-full max-w-2xl mx-auto z-10">
                <div className="shadow-2xl rounded-lg overflow-hidden border border-gray-700/50">
                    <div className="px-6 py-5 border-b border-gray-700/50">
                        <h1 className="text-xl font-bold text-center text-gray-100">Generador de Clave de Activación</h1>
                    </div>
                    <div className="p-6 space-y-6">
                        <div className="space-y-4">
                             <h2 className="text-lg font-semibold text-indigo-400">Datos para Generación</h2>
                             <div>
                                <label htmlFor="combined-input" className="block text-sm font-medium text-gray-400 mb-1">
                                    ID de Máquina y MAC
                                </label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <ChipIcon className="h-5 w-5 text-gray-500" />
                                    </div>
                                    <input
                                        id="combined-input"
                                        type="text"
                                        value={combinedInput}
                                        onChange={(e) => setCombinedInput(e.target.value)}
                                        className="w-full bg-gray-900 border border-gray-600 rounded-md pl-10 pr-4 py-2 text-gray-200 focus:ring-indigo-500 focus:border-indigo-500"
                                        placeholder="ej: 5db43e14-... , 43:76:55:..."
                                    />
                                </div>
                            </div>
                            <div>
                                <label htmlFor="secret-key" className="block text-sm font-medium text-gray-400 mb-1">
                                    Clave Secreta
                                </label>
                                 <div className="flex items-center space-x-2">
                                    <div className="relative flex-grow">
                                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <LockIcon className="h-5 w-5 text-gray-500" />
                                        </div>
                                        <input
                                            id="secret-key"
                                            type="password"
                                            value={secretKey}
                                            onChange={(e) => setSecretKey(e.target.value)}
                                            readOnly={isSecretKeySaved}
                                            className="w-full bg-gray-900 border border-gray-600 rounded-md pl-10 pr-4 py-2 text-gray-200 focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-700"
                                            placeholder="Ingrese su clave secreta"
                                        />
                                    </div>
                                    {isSecretKeySaved ? (
                                        <button onClick={handleDeleteSecretKey} title="Borrar clave guardada" className="p-2.5 bg-red-600 hover:bg-red-700 rounded-md transition-colors flex-shrink-0">
                                            <TrashIcon className="h-5 w-5 text-white" />
                                        </button>
                                    ) : (
                                        <button onClick={handleSaveSecretKey} disabled={!secretKey || isSecretKeySaved} className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-md disabled:bg-gray-500 disabled:cursor-not-allowed text-white text-sm font-medium transition-colors flex-shrink-0">
                                            Guardar
                                        </button>
                                    )}
                                </div>
                            </div>
                            
                            <button
                                onClick={handleGenerateKey}
                                disabled={isGenerating}
                                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 focus:ring-offset-gray-800 disabled:bg-indigo-500 disabled:opacity-75 transition-colors"
                            >
                                {isGenerating ? 'Generando...' : 'Generar Clave'}
                            </button>
                            {error && <p className="text-sm text-red-400 mt-2 text-center">{error}</p>}
                        </div>

                        <hr className="border-gray-700" />

                        <div className="space-y-4">
                            <h2 className="text-lg font-semibold text-indigo-400">Resultado</h2>
                            <div className="flex flex-col sm:flex-row items-center sm:items-start sm:space-x-6 space-y-4 sm:space-y-0">
                                <div className="flex-1 w-full">
                                    <DisplayField
                                        label="Clave de Activación (12 Dígitos)"
                                        value={activationKey}
                                        icon={<KeyIcon className="h-5 w-5 text-gray-400"/>}
                                    />
                                </div>
                                <div className="flex-shrink-0">
                                     <label className="block text-sm font-medium text-gray-400 mb-2 text-center sm:text-left">Código QR</label>
                                     <div className="w-36 h-36 bg-gray-700/50 rounded-lg flex items-center justify-center p-2 border border-gray-600/50">
                                         {qrCodeDataUrl ? (
                                             <img src={qrCodeDataUrl} alt="Código QR de la clave de activación" className="w-full h-full object-contain rounded-md" />
                                         ) : (
                                            <div className="w-full h-full flex items-center justify-center">
                                                <QrCodeIcon className="w-16 h-16 text-gray-500/70" />
                                            </div>
                                         )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr className="border-gray-700" />

                        <div className="space-y-4">
                           <div className="flex justify-between items-center">
                                <h2 className="text-lg font-semibold text-indigo-400">Historial de Activaciones</h2>
                                {history.length > 0 && (
                                    <button onClick={handleClearHistory} className="flex items-center text-sm text-red-400 hover:text-red-300 transition-colors">
                                        <TrashIcon className="h-4 w-4 mr-1.5" />
                                        Limpiar Historial
                                    </button>
                                )}
                           </div>
                            <div className="max-h-60 overflow-y-auto space-y-3 pr-2 -mr-2">
                                {history.length > 0 ? (
                                    history.map(entry => (
                                        <div key={entry.id} className="bg-gray-700/50 p-3 rounded-lg flex justify-between items-center hover:bg-gray-700 transition-colors">
                                            <div className="flex-1 overflow-hidden mr-2">
                                                <p className="text-sm font-medium text-gray-300 truncate" title={entry.machineId}>
                                                   <span className="font-semibold text-gray-400">ID:</span> {entry.machineId}
                                                </p>
                                                <p className="text-base font-bold text-indigo-300 tracking-wider">
                                                    {entry.activationKey}
                                                </p>
                                                <p className="text-xs text-gray-500 flex items-center mt-1">
                                                    <ClockIcon className="h-3 w-3 mr-1.5" />
                                                    {new Date(entry.timestamp).toLocaleString()}
                                                </p>
                                            </div>
                                            <button
                                                onClick={() => handleCopyHistoryKey(entry.activationKey)}
                                                className="p-2 rounded-md hover:bg-gray-600 flex-shrink-0"
                                                title="Copiar clave"
                                            >
                                                {copiedKey === entry.activationKey ? (
                                                    <CheckIcon className="h-5 w-5 text-green-400" />
                                                ) : (
                                                    <ClipboardIcon className="h-5 w-5 text-gray-400" />
                                                )}
                                            </button>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center text-gray-500 py-6">
                                        <p>No hay historial de activaciones.</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
                <footer className="text-center mt-6 text-xs text-gray-500">
                    <p>Ingrese el ID de Máquina/MAC del cliente y su clave secreta para generar la clave.</p>
                </footer>
            </div>
        </div>
    );
};

export default App;
