

import React, { useState, useCallback } from 'react';
import { ClipboardIcon, CheckIcon } from './icons';

interface DisplayFieldProps {
    label: string;
    value: string;
    icon?: React.ReactNode;
}

const DisplayField: React.FC<DisplayFieldProps> = ({ label, value, icon }) => {
    const [copied, setCopied] = useState(false);

    const handleCopy = useCallback(() => {
        if (value) {
            navigator.clipboard.writeText(value);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        }
    }, [value]);

    return (
        <div>
            <label className="block text-sm font-medium text-gray-400">{label}</label>
            <div className="mt-1 flex rounded-md shadow-sm">
                <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-600 bg-gray-700 text-gray-300 text-sm">
                    {icon}
                </span>
                <div className="relative flex items-stretch flex-grow focus-within:z-10">
                    <input
                        type="text"
                        readOnly
                        value={value}
                        className="flex-1 block w-full rounded-none bg-gray-900 border-gray-600 text-gray-200 placeholder-gray-500 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm px-3 py-2"
                        placeholder={!value ? "..." : ""}
                    />
                </div>
                <button
                    onClick={handleCopy}
                    type="button"
                    className="-ml-px relative inline-flex items-center space-x-2 px-4 py-2 border border-gray-600 text-sm font-medium rounded-r-md text-gray-300 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 disabled:opacity-50"
                    disabled={!value}
                >
                    {copied ? (
                        <CheckIcon className="h-5 w-5 text-green-400" />
                    ) : (
                        <ClipboardIcon className="h-5 w-5" />
                    )}
                </button>
            </div>
        </div>
    );
};

export default DisplayField;