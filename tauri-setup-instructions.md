# Instrucciones para completar la configuración de Tauri

## Paso 1: Instalar Rust (REQUERIDO)
1. Ve a https://rustup.rs/
2. Descarga el instalador para Windows
3. Ejecuta el instalador y acepta las opciones por defecto
4. **IMPORTANTE**: Reinicia tu terminal/PowerShell después de la instalación
5. Verifica la instalación ejecutando: `rustc --version`

## Paso 2: In<PERSON><PERSON><PERSON> (ejecutar después de instalar Rust)
Una vez que Rust esté instalado, ejecuta:
```powershell
npx tauri init
```

Cuando te pregunte:
- **What is your app name?**: Generador de Claves de Activación
- **What should the window title be?**: Generador de Claves de Activación
- **Where are your web assets located?**: dist
- **What is the URL of your dev server?**: http://localhost:1420
- **What is your frontend dev command?**: npm run dev
- **What is your frontend build command?**: npm run build

## Paso 3: Configurar el almacenamiento
Después de la inicialización, necesitaremos:
1. Configurar el plugin de store para reemplazar localStorage
2. Modificar el código React para usar las APIs de Tauri
3. Configurar los permisos de la aplicación

## Paso 4: Build del ejecutable
```powershell
npm run tauri:build
```

El archivo .exe se generará en `src-tauri/target/release/`
